@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo Starting development environment...

REM Check if .env file exists
if not exist ".env" (
    echo .env file not found, copying example file...
    copy .env.example .env >nul
    if errorlevel 1 (
        echo Failed to copy .env.example
        exit /b 1
    )
    echo .env file created successfully
)

REM Check dependencies
echo Checking and updating dependencies...
go mod tidy
if errorlevel 1 (
    echo Failed to update dependencies
    exit /b 1
)

REM Check if wire is installed
where wire >nul 2>&1
if errorlevel 1 (
    echo Wire tool not found, installing...
    go install github.com/google/wire/cmd/wire@latest
    if errorlevel 1 (
        echo Failed to install wire, please install manually:
        echo go install github.com/google/wire/cmd/wire@latest
        exit /b 1
    )
    echo Wire installed successfully
)

REM Generate dependency injection code
echo Generating dependency injection code...
cd internal\wire
wire
set WIRE_RESULT=%errorlevel%
cd ..\..
if not %WIRE_RESULT%==0 (
    echo Failed to generate dependency injection code
    exit /b 1
)
echo Dependency injection code generated successfully

REM Check if swag is installed
where swag >nul 2>&1
if errorlevel 1 (
    echo Swag tool not found, installing...
    go install github.com/swaggo/swag/cmd/swag@latest
    if errorlevel 1 (
        echo Failed to install swag, please install manually:
        echo go install github.com/swaggo/swag/cmd/swag@latest
    ) else (
        echo Swag installed successfully
    )
)

REM Generate Swagger documentation
echo Generating Swagger API documentation...
swag init -g main.go
if errorlevel 1 (
    echo Failed to generate Swagger documentation
) else (
    echo Swagger documentation generated successfully
)

REM Start application
echo Starting application...
echo Swagger documentation URL - http://localhost:8084/swagger/index.html
go run main.go
