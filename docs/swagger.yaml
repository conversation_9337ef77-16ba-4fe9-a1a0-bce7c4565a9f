definitions:
  dto.LoginCodeRequest:
    properties:
      code:
        example: "123456"
        type: string
      phone:
        example: "13812345678"
        type: string
    required:
    - code
    - phone
    type: object
  dto.SendSMSCodeRequest:
    properties:
      phone:
        example: "13812345678"
        type: string
    required:
    - phone
    type: object
  dto.UpdateUsernameRequest:
    properties:
      username:
        example: 新用户名
        maxLength: 20
        minLength: 2
        type: string
    required:
    - username
    type: object
  enum.UserType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      UserTypeRegular: 普通用户
      UserTypeVIP: 会员
    x-enum-varnames:
    - UserTypeRegular
    - UserTypeVIP
  vo.ErrorAPIResponse:
    properties:
      code:
        description: 业务状态码
        example: 400
        type: integer
      data:
        description: 响应数据，通常为null
      message:
        description: 错误消息
        example: 请求参数错误
        type: string
    type: object
  vo.HelpListResponse:
    properties:
      list:
        description: 帮助信息列表
        items:
          $ref: '#/definitions/vo.HelpResponse'
        type: array
    type: object
  vo.HelpResponse:
    properties:
      description:
        description: 描述
        example: 平台现在支持微信、支付宝、网银提现...
        type: string
      id:
        description: 帮助ID
        example: 1
        type: integer
      image_url:
        description: 图片链接
        example: https://example.com/help.jpg
        type: string
      sort:
        description: 排序
        example: 1
        type: integer
      title:
        description: 标题
        example: 平台是如何结算,结算时间是多久?
        type: string
    type: object
  vo.ProductCategoryResponse:
    properties:
      children:
        items:
          $ref: '#/definitions/vo.ProductSummaryResponse'
        type: array
      id:
        example: 1
        type: integer
      name:
        example: 电商卡
        type: string
    type: object
  vo.ProductSearchListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo.ProductSearchResponse'
        type: array
    type: object
  vo.ProductSearchResponse:
    properties:
      discount_tip:
        example: 9.5折起
        type: string
      id:
        example: 1
        type: integer
      image_url1:
        example: https://example.com/image.jpg
        type: string
      name:
        example: 京东E卡
        type: string
    type: object
  vo.ProductSummaryResponse:
    properties:
      id:
        example: 1
        type: integer
      image_url1:
        example: https://example.com/image.jpg
        type: string
      name:
        example: 京东E卡
        type: string
    type: object
  vo.SuccessAPIResponse:
    properties:
      code:
        description: 业务状态码
        example: 200
        type: integer
      data:
        description: 响应数据
      message:
        description: 响应消息
        example: 操作成功
        type: string
    type: object
  vo.SystemConfigResponse:
    properties:
      hot_search:
        example:
        - 京东e卡
        - 携程
        - 微信立减金
        - 支付宝消费券
        items:
          type: string
        type: array
    type: object
  vo.TokenResponse:
    properties:
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  vo.UploadResponse:
    properties:
      filename:
        description: 文件名
        example: 123456789.jpg
        type: string
      url:
        description: 文件URL
        example: https://cdn.avrilko.com/speed-fox/avatar/123456789.jpg
        type: string
    type: object
  vo.UserResponse:
    properties:
      avatar:
        example: http://example.com/avatar.jpg
        type: string
      balance:
        description: 余额
        example: 100.5
        type: number
      created_at:
        description: 创建时间
        example: "2023-01-01T12:00:00Z"
        type: string
      friend_count:
        description: 好友数
        example: 10
        type: integer
      id:
        example: 1
        type: integer
      open_id:
        example: oNHwxjgrzgL9H_A2pGLSMuME-X-Q
        type: string
      phone:
        example: "13812345678"
        type: string
      promotion_earning:
        description: 推广收益
        example: 50
        type: number
      user_type:
        allOf:
        - $ref: '#/definitions/enum.UserType'
        description: 用户类型 1:普通用户 2:中级会员 3:高级会员
        example: 1
      username:
        example: johndoe
        type: string
    type: object
host: localhost:8084
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: https://github.com/avrilko-go/recycle-server
  description: 京海回收服务器 API 文档
  license:
    name: MIT License
    url: https://opensource.org/licenses/MIT
  title: 京海回收服务器 API
  version: "1.0"
paths:
  /api/auth/login-code:
    post:
      consumes:
      - application/json
      description: 用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册
      parameters:
      - description: 登录信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.LoginCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回token
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.TokenResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 验证码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 验证码登录
      tags:
      - API/认证管理
  /api/auth/sms/code:
    post:
      consumes:
      - application/json
      description: 向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次
      parameters:
      - description: 发送验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.SendSMSCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 发送成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "429":
          description: 发送频率限制
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 发送短信验证码
      tags:
      - API/认证管理
  /api/common/helps:
    get:
      consumes:
      - application/json
      description: 获取所有帮助信息，按排序字段升序排列
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.HelpListResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取所有帮助信息
      tags:
      - API/通用接口
  /api/common/system-config:
    get:
      consumes:
      - application/json
      description: 获取系统配置信息，包括热门搜索等
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.SystemConfigResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取系统配置
      tags:
      - API/通用接口
  /api/products/categories:
    get:
      consumes:
      - application/json
      description: 获取所有分类及其下的产品信息，按分类分组返回
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/vo.ProductCategoryResponse'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取带分类分组的产品信息
      tags:
      - API/产品管理
  /api/products/search:
    get:
      consumes:
      - application/json
      description: 根据关键词模糊搜索产品名称，返回匹配的产品列表
      parameters:
      - description: 搜索关键词
        in: query
        name: keyword
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 搜索成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.ProductSearchListResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 搜索产品
      tags:
      - API/产品管理
  /api/upload/avatar:
    post:
      consumes:
      - multipart/form-data
      description: 上传用户头像，支持jpg、png、webp格式，最大7MB
      parameters:
      - description: 头像文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 上传成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.UploadResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 上传头像
      tags:
      - API/文件上传
  /api/users/avatar:
    put:
      consumes:
      - multipart/form-data
      description: 修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB
      parameters:
      - description: 头像文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 修改用户头像
      tags:
      - API/用户管理
  /api/users/info:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的详细信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.UserResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取当前登录用户信息
      tags:
      - API/用户管理
  /api/users/logout:
    post:
      consumes:
      - application/json
      description: 用户退出登录，清除登录状态
      produces:
      - application/json
      responses:
        "200":
          description: 退出成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 退出登录
      tags:
      - API/用户管理
  /api/users/username:
    put:
      consumes:
      - application/json
      description: 修改当前登录用户的用户名
      parameters:
      - description: 修改用户名请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateUsernameRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 修改用户名
      tags:
      - API/用户管理
schemes:
- http
- https
swagger: "2.0"
