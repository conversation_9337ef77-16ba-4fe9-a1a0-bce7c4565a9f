// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "API Support",
            "url": "https://github.com/avrilko-go/recycle-server",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/auth/login-code": {
            "post": {
                "description": "用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "验证码登录",
                "parameters": [
                    {
                        "description": "登录信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.LoginCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功，返回token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.TokenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "验证码错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/sms/code": {
            "post": {
                "description": "向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "发送短信验证码",
                "parameters": [
                    {
                        "description": "发送验证码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SendSMSCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "发送成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "429": {
                        "description": "发送频率限制",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/common/helps": {
            "get": {
                "description": "获取所有帮助信息，按排序字段升序排列",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/通用接口"
                ],
                "summary": "获取所有帮助信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.HelpListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/common/system-config": {
            "get": {
                "description": "获取系统配置信息，包括热门搜索等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/通用接口"
                ],
                "summary": "获取系统配置",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.SystemConfigResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/products/categories": {
            "get": {
                "description": "获取所有分类及其下的产品信息，按分类分组返回",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/产品管理"
                ],
                "summary": "获取带分类分组的产品信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/vo.ProductCategoryResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/products/search": {
            "get": {
                "description": "根据关键词模糊搜索产品名称，返回匹配的产品列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/产品管理"
                ],
                "summary": "搜索产品",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "keyword",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "搜索成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ProductSearchListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/upload/avatar": {
            "post": {
                "description": "上传用户头像，支持jpg、png、webp格式，最大7MB",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/文件上传"
                ],
                "summary": "上传头像",
                "parameters": [
                    {
                        "type": "file",
                        "description": "头像文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上传成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UploadResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "413": {
                        "description": "文件过大",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "415": {
                        "description": "不支持的文件类型",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/avatar": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "修改用户头像",
                "parameters": [
                    {
                        "type": "file",
                        "description": "头像文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "413": {
                        "description": "文件过大",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "415": {
                        "description": "不支持的文件类型",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/info": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取当前登录用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "获取当前登录用户信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/logout": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "用户退出登录，清除登录状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "退出登录",
                "responses": {
                    "200": {
                        "description": "退出成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/username": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "修改当前登录用户的用户名",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "修改用户名",
                "parameters": [
                    {
                        "description": "修改用户名请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UpdateUsernameRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dto.LoginCodeRequest": {
            "type": "object",
            "required": [
                "code",
                "phone"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "123456"
                },
                "phone": {
                    "type": "string",
                    "example": "13812345678"
                }
            }
        },
        "dto.SendSMSCodeRequest": {
            "type": "object",
            "required": [
                "phone"
            ],
            "properties": {
                "phone": {
                    "type": "string",
                    "example": "13812345678"
                }
            }
        },
        "dto.UpdateUsernameRequest": {
            "type": "object",
            "required": [
                "username"
            ],
            "properties": {
                "username": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 2,
                    "example": "新用户名"
                }
            }
        },
        "enum.UserType": {
            "type": "integer",
            "enum": [
                1,
                2
            ],
            "x-enum-comments": {
                "UserTypeRegular": "普通用户",
                "UserTypeVIP": "会员"
            },
            "x-enum-varnames": [
                "UserTypeRegular",
                "UserTypeVIP"
            ]
        },
        "vo.ErrorAPIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "业务状态码",
                    "type": "integer",
                    "example": 400
                },
                "data": {
                    "description": "响应数据，通常为null"
                },
                "message": {
                    "description": "错误消息",
                    "type": "string",
                    "example": "请求参数错误"
                }
            }
        },
        "vo.HelpListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "帮助信息列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.HelpResponse"
                    }
                }
            }
        },
        "vo.HelpResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string",
                    "example": "平台现在支持微信、支付宝、网银提现..."
                },
                "id": {
                    "description": "帮助ID",
                    "type": "integer",
                    "example": 1
                },
                "image_url": {
                    "description": "图片链接",
                    "type": "string",
                    "example": "https://example.com/help.jpg"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer",
                    "example": 1
                },
                "title": {
                    "description": "标题",
                    "type": "string",
                    "example": "平台是如何结算,结算时间是多久?"
                }
            }
        },
        "vo.ProductCategoryResponse": {
            "type": "object",
            "properties": {
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ProductSummaryResponse"
                    }
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "电商卡"
                }
            }
        },
        "vo.ProductSearchListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ProductSearchResponse"
                    }
                }
            }
        },
        "vo.ProductSearchResponse": {
            "type": "object",
            "properties": {
                "discount_tip": {
                    "type": "string",
                    "example": "9.5折起"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "image_url1": {
                    "type": "string",
                    "example": "https://example.com/image.jpg"
                },
                "name": {
                    "type": "string",
                    "example": "京东E卡"
                }
            }
        },
        "vo.ProductSummaryResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "image_url1": {
                    "type": "string",
                    "example": "https://example.com/image.jpg"
                },
                "name": {
                    "type": "string",
                    "example": "京东E卡"
                }
            }
        },
        "vo.SuccessAPIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "业务状态码",
                    "type": "integer",
                    "example": 200
                },
                "data": {
                    "description": "响应数据"
                },
                "message": {
                    "description": "响应消息",
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "vo.SystemConfigResponse": {
            "type": "object",
            "properties": {
                "hot_search": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "京东e卡",
                        "携程",
                        "微信立减金",
                        "支付宝消费券"
                    ]
                }
            }
        },
        "vo.TokenResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                }
            }
        },
        "vo.UploadResponse": {
            "type": "object",
            "properties": {
                "filename": {
                    "description": "文件名",
                    "type": "string",
                    "example": "123456789.jpg"
                },
                "url": {
                    "description": "文件URL",
                    "type": "string",
                    "example": "https://cdn.avrilko.com/speed-fox/avatar/123456789.jpg"
                }
            }
        },
        "vo.UserResponse": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string",
                    "example": "http://example.com/avatar.jpg"
                },
                "balance": {
                    "description": "余额",
                    "type": "number",
                    "example": 100.5
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "open_id": {
                    "type": "string",
                    "example": "oNHwxjgrzgL9H_A2pGLSMuME-X-Q"
                },
                "phone": {
                    "type": "string",
                    "example": "13812345678"
                },
                "user_type": {
                    "description": "用户类型 1:普通用户 2:会员",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.UserType"
                        }
                    ],
                    "example": 1
                },
                "username": {
                    "type": "string",
                    "example": "johndoe"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8084",
	BasePath:         "",
	Schemes:          []string{"http", "https"},
	Title:            "京海回收服务器 API",
	Description:      "京海回收服务器 API 文档",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
