package api

import (
	"recycle-server/internal/controllers/api"

	"github.com/gin-gonic/gin"
)

// RegisterCommonRoutes 注册通用路由
func RegisterCommonRoutes(router *gin.RouterGroup, commonController *api.CommonController) {
	// 通用路由组 - 不需要认证
	commonGroup := router.Group("/common")
	{
		// 获取系统配置
		commonGroup.GET("/system-config", commonController.SystemConfig)
		// 获取所有帮助信息
		commonGroup.GET("/helps", commonController.GetAllHelps)
	}
}
