package pkg

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"recycle-server/config"
	"recycle-server/internal/models"

	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"gorm.io/gorm/utils"
)

// DB 全局数据库实例
var DB *gorm.DB

// ZapGormLogger 实现gorm的Logger接口的Zap适配器
type ZapGormLogger struct {
	ZapLogger                 *zap.Logger
	LogLevel                  logger.LogLevel
	SlowThreshold             time.Duration
	IgnoreRecordNotFoundError bool
}

// NewZapGormLogger 创建一个新的ZapGormLogger
func NewZapGormLogger(zapLogger *zap.Logger, config logger.Config) *ZapGormLogger {
	return &ZapGormLogger{
		ZapLogger:                 zapLogger,
		LogLevel:                  config.LogLevel,
		SlowThreshold:             config.SlowThreshold,
		IgnoreRecordNotFoundError: config.IgnoreRecordNotFoundError,
	}
}

// LogMode 设置日志级别
func (l *ZapGormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newLogger := *l
	newLogger.LogLevel = level
	return &newLogger
}

// Info 打印信息日志
func (l *ZapGormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Info {
		l.ZapLogger.Info(fmt.Sprintf(msg, data...))
	}
}

// Warn 打印警告日志
func (l *ZapGormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		l.ZapLogger.Warn(fmt.Sprintf(msg, data...))
	}
}

// Error 打印错误日志
func (l *ZapGormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		l.ZapLogger.Error(fmt.Sprintf(msg, data...))
	}
}

// Trace 打印SQL日志
func (l *ZapGormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	switch {
	case err != nil && l.LogLevel >= logger.Error && (!errors.Is(err, gorm.ErrRecordNotFound) || !l.IgnoreRecordNotFoundError):
		sql, rows := fc()
		l.ZapLogger.Error("GORM SQL错误",
			zap.Error(err),
			zap.Duration("elapsed", elapsed),
			zap.String("sql", sql),
			zap.Int64("rows", rows),
			zap.String("caller", utils.FileWithLineNum()))
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= logger.Warn:
		sql, rows := fc()
		l.ZapLogger.Warn("GORM慢SQL",
			zap.Duration("elapsed", elapsed),
			zap.String("sql", sql),
			zap.Int64("rows", rows),
			zap.String("caller", utils.FileWithLineNum()))
	case l.LogLevel >= logger.Info:
		sql, rows := fc()
		l.ZapLogger.Debug("GORM SQL",
			zap.Duration("elapsed", elapsed),
			zap.String("sql", sql),
			zap.Int64("rows", rows),
			zap.String("caller", utils.FileWithLineNum()))
	}
}

// InitDB 初始化数据库连接
func InitDB(cfg *config.Config) error {
	var err error
	var dsn string

	switch cfg.Database.Connection {
	case "mysql":
		// 构建连接字符串时使用配置项
		charset := "utf8mb4"
		if cfg.Database.Charset != "" {
			charset = cfg.Database.Charset
		}

		parseTime := "True"
		if !cfg.Database.ParseTime {
			parseTime = "False"
		}

		timeZone := "Local"
		if cfg.Database.TimeZone != "" {
			timeZone = cfg.Database.TimeZone
		}

		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%s&loc=%s",
			cfg.Database.Username,
			cfg.Database.Password,
			cfg.Database.Host,
			cfg.Database.Port,
			cfg.Database.Database,
			charset,
			parseTime,
			timeZone)
	default:
		return fmt.Errorf("不支持的数据库类型: %s", cfg.Database.Connection)
	}

	// 配置GORM
	gormConfig := &gorm.Config{}

	// 命名策略
	gormConfig.NamingStrategy = schema.NamingStrategy{
		TablePrefix:   cfg.Database.GormTablePrefix,
		SingularTable: cfg.Database.GormSingularTable,
	}

	// 跳过默认事务
	if cfg.Database.GormSkipDefaultTx {
		gormConfig.SkipDefaultTransaction = true
	}

	// 使用预处理语句
	if cfg.Database.GormPrepareStmt {
		gormConfig.PrepareStmt = true
	}

	// 关闭外键约束
	if cfg.Database.GormCloseForeignKey {
		gormConfig.DisableForeignKeyConstraintWhenMigrating = true
	}

	// 日志配置
	if cfg.Database.GormCoverLogger {
		logLevel := logger.Info
		switch strings.ToLower(cfg.Database.LogLevel) {
		case "silent":
			logLevel = logger.Silent
		case "error":
			logLevel = logger.Error
		case "warn":
			logLevel = logger.Warn
		case "info":
			logLevel = logger.Info
		}

		// 使用Zap日志代替标准日志
		gormConfig.Logger = NewZapGormLogger(
			Logger,
			logger.Config{
				SlowThreshold:             cfg.Database.SlowSQL,
				LogLevel:                  logLevel,
				IgnoreRecordNotFoundError: cfg.Database.IgnoreRecordNotFoundError,
			},
		)
	}

	// 连接数据库
	DB, err = gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 设置最大空闲连接数
	maxIdleConn := 10
	if cfg.Database.MaxIdleConn > 0 {
		maxIdleConn = cfg.Database.MaxIdleConn
	}
	sqlDB.SetMaxIdleConns(maxIdleConn)

	// 设置最大连接数
	maxOpenConn := 100
	if cfg.Database.MaxOpenConn > 0 {
		maxOpenConn = cfg.Database.MaxOpenConn
	}
	sqlDB.SetMaxOpenConns(maxOpenConn)

	// 设置连接的最大可复用时间
	connMaxLifetime := time.Hour
	if cfg.Database.ConnMaxLifetime > 0 {
		connMaxLifetime = cfg.Database.ConnMaxLifetime
	}
	sqlDB.SetConnMaxLifetime(connMaxLifetime)

	// 设置连接的最大空闲时间
	if cfg.Database.ConnMaxIdleTime > 0 {
		sqlDB.SetConnMaxIdleTime(cfg.Database.ConnMaxIdleTime)
	}

	return nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}

// NewDatabase 初始化并返回数据库连接（用于依赖注入）
func NewDatabase(cfg *config.Config) (*gorm.DB, error) {
	err := InitDB(cfg)
	if err != nil {
		return nil, err
	}
	return DB, nil
}

// AutoMigrateDB 自动迁移数据库表结构
func AutoMigrateDB() error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 在这里添加需要自动迁移的模型
	err := DB.AutoMigrate(
		&models.User{},
		&models.Category{},
		&models.Product{},
		&models.FaceValue{},
		&models.Help{},
		// 添加其他模型...
	)

	if err != nil {
		return fmt.Errorf("自动迁移失败: %w", err)
	}

	// 添加表注释
	tableComments := map[string]string{
		"user":       "用户表 - 存储系统用户信息",
		"category":   "分类表 - 存储分类信息，包含热门模板、大学专业、设计风格等分类",
		"product":    "产品表 - 存储产品信息，包含产品名称、分类、图片、规则等详细信息",
		"face_value": "面值表 - 存储产品面值信息，包含面值、费率等详细信息",
		"help":       "帮助表 - 存储帮助信息，包含标题、描述、图片链接等详细信息",

		// 添加其他表注释...
	}
	addTableComments(tableComments)

	return nil
}

// addTableComment 添加单个表注释
func addTableComment(tableName, comment string) {
	// 针对MySQL添加表注释，使用反引号包围表名以处理保留关键字
	DB.Exec(fmt.Sprintf("ALTER TABLE `%s` COMMENT = '%s'", tableName, comment))
}

// addTableComments 批量添加表注释
func addTableComments(comments map[string]string) {
	for table, comment := range comments {
		addTableComment(table, comment)
	}
}
