package dto

import "mime/multipart"

// SendSMSCodeRequest 发送短信验证码请求DTO
type SendSMSCodeRequest struct {
	Phone string `json:"phone" binding:"required" example:"13812345678" label:"手机号码"`
}

// LoginCodeRequest 验证码登录请求DTO
type LoginCodeRequest struct {
	Phone string `json:"phone" binding:"required" example:"13812345678" label:"手机号码"`
	Code  string `json:"code" binding:"required" example:"123456" label:"验证码"`
}

// UpdateUsernameRequest 修改用户名请求DTO
type UpdateUsernameRequest struct {
	Username string `json:"username" binding:"required,min=2,max=20" example:"新用户名" label:"用户名"`
}

// UpdateAvatarRequest 修改用户头像请求DTO（文件上传）
type UpdateAvatarRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required" swaggerignore:"true"` // 头像文件
}
