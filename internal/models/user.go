package models

import (
	"recycle-server/internal/enum"
)

// User 用户模型
type User struct {
	ID       uint            `json:"id" gorm:"primaryKey"`
	Username string          `json:"username" gorm:"size:20;not null;comment:用户名"`
	OpenID   string          `json:"open_id" gorm:"size:50;index;comment:微信OpenID"`
	Phone    string          `json:"phone" gorm:"size:20;index;comment:手机号"`
	Avatar   string          `json:"avatar" gorm:"size:255;comment:头像"`
	Balance  float64         `json:"balance" gorm:"type:decimal(10,2);default:0.00;comment:余额"`
	Status   enum.UserStatus `json:"status" gorm:"type:tinyint(1);default:1;index;comment:状态 1:启用 2:禁用"`
	UserType enum.UserType   `json:"user_type" gorm:"column:user_type;type:tinyint(1);default:1;index;comment:用户类型 1:普通用户 2:会员"`

	Base
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}
