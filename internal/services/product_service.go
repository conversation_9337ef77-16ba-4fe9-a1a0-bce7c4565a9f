package services

import (
	"strings"

	"recycle-server/config"
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/vo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ProductService 产品服务接口
type ProductService interface {
	// GetProductsWithCategories 获取带分类分组的产品信息
	GetProductsWithCategories() ([]vo.ProductCategoryResponse, error)
	// SearchProducts 根据关键词搜索产品
	SearchProducts(keyword string) (*vo.ProductSearchListResponse, error)
}

// CategoryWithProducts 分类及其产品信息
type CategoryWithProducts struct {
	ID       uint             `json:"id"`
	Name     string           `json:"name"`
	Children []ProductSummary `json:"children"`
}

// ProductSummary 产品摘要信息
type ProductSummary struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	ImageURL1 string `json:"image_url1"`
}

// productService 产品服务实现
type productService struct {
	db     *gorm.DB
	logger *zap.Logger
	config *config.Config
}

// NewProductService 创建产品服务
func NewProductService(db *gorm.DB, logger *zap.Logger, cfg *config.Config) ProductService {
	return &productService{
		db:     db,
		logger: logger,
		config: cfg,
	}
}

// GetProductsWithCategories 获取带分类分组的产品信息
func (s *productService) GetProductsWithCategories() ([]vo.ProductCategoryResponse, error) {
	var categories []models.Category
	var products []models.Product

	// 获取所有分类，按排序字段排序
	if err := s.db.Order("sort ASC, id ASC").Find(&categories).Error; err != nil {
		s.logger.Error("获取分类列表失败", zap.Error(err))
		return nil, exception.ErrCategoryQueryFailed
	}

	// 获取所有产品，按排序字段排序
	if err := s.db.Order("sort ASC, id ASC").Find(&products).Error; err != nil {
		s.logger.Error("获取产品列表失败", zap.Error(err))
		return nil, exception.ErrProductQueryFailed
	}

	// 构建分类ID到产品列表的映射
	categoryProductsMap := make(map[uint][]ProductSummary)
	for _, product := range products {
		categoryProductsMap[product.CategoryID] = append(categoryProductsMap[product.CategoryID], ProductSummary{
			ID:        product.ID,
			Name:      product.Name,
			ImageURL1: product.ImageURL1,
		})
	}

	// 构建最终结果
	var result []vo.ProductCategoryResponse
	for _, category := range categories {
		var children []vo.ProductSummaryResponse
		for _, product := range categoryProductsMap[category.ID] {
			// 拼接后端域名到图片URL
			imageURL := s.buildImageURL(product.ImageURL1)

			children = append(children, vo.ProductSummaryResponse{
				ID:        product.ID,
				Name:      product.Name,
				ImageURL1: imageURL,
			})
		}

		// 如果没有产品，设置为空数组而不是nil
		if children == nil {
			children = []vo.ProductSummaryResponse{}
		}

		result = append(result, vo.ProductCategoryResponse{
			ID:       category.ID,
			Name:     category.Name,
			Children: children,
		})
	}

	s.logger.Info("获取带分类分组的产品信息成功",
		zap.Int("categories_count", len(categories)),
		zap.Int("products_count", len(products)))

	return result, nil
}

// buildImageURL 构建完整的图片URL
func (s *productService) buildImageURL(imageURL string) string {
	// 如果图片URL为空，返回空字符串
	if imageURL == "" {
		return ""
	}

	// 如果图片URL已经是完整的URL（包含http://或https://），直接返回
	if strings.HasPrefix(imageURL, "http://") || strings.HasPrefix(imageURL, "https://") {
		return imageURL
	}

	// 如果后端域名配置为空，直接返回原URL
	if s.config.BackendDomain == "" {
		return imageURL
	}

	// 确保后端域名不以/结尾，图片URL以/开头
	backendDomain := strings.TrimSuffix(s.config.BackendDomain, "/")
	if !strings.HasPrefix(imageURL, "/") {
		imageURL = "/" + imageURL
	}

	return backendDomain + imageURL
}

// SearchProducts 根据关键词搜索产品
func (s *productService) SearchProducts(keyword string) (*vo.ProductSearchListResponse, error) {
	// 如果关键词为空，返回空结果
	if strings.TrimSpace(keyword) == "" {
		return &vo.ProductSearchListResponse{
			List: []vo.ProductSearchResponse{},
		}, nil
	}

	var products []models.Product

	// 使用LIKE进行模糊搜索，搜索产品名称
	if err := s.db.Where("name LIKE ?", "%"+keyword+"%").
		Order("sort ASC, id ASC").
		Find(&products).Error; err != nil {
		s.logger.Error("搜索产品失败", zap.String("keyword", keyword), zap.Error(err))
		return nil, exception.ErrProductQueryFailed
	}

	// 构建搜索结果
	var searchResults []vo.ProductSearchResponse
	for _, product := range products {
		// 拼接后端域名到图片URL
		imageURL := s.buildImageURL(product.ImageURL1)

		searchResults = append(searchResults, vo.ProductSearchResponse{
			ID:          product.ID,
			Name:        product.Name,
			ImageURL1:   imageURL,
			DiscountTip: product.DiscountTip,
		})
	}

	// 如果没有结果，设置为空数组而不是nil
	if searchResults == nil {
		searchResults = []vo.ProductSearchResponse{}
	}

	s.logger.Info("搜索产品成功",
		zap.String("keyword", keyword),
		zap.Int("results_count", len(searchResults)))

	return &vo.ProductSearchListResponse{
		List: searchResults,
	}, nil
}
