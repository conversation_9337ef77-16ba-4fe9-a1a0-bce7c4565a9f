package vo

import (
	"time"

	"recycle-server/internal/enum"
)

// UserResponse 用户信息响应视图对象
type UserResponse struct {
	ID               uint          `json:"id" example:"1"`
	Username         string        `json:"username" example:"johndoe"`
	Phone            string        `json:"phone" example:"13812345678"`
	OpenID           string        `json:"open_id" example:"oNHwxjgrzgL9H_A2pGLSMuME-X-Q"`
	Avatar           string        `json:"avatar" example:"http://example.com/avatar.jpg"`
	Balance          float64       `json:"balance" example:"100.50"`                  // 余额
	FriendCount      int           `json:"friend_count" example:"10"`                 // 好友数
	PromotionEarning float64       `json:"promotion_earning" example:"50.00"`         // 推广收益
	CreatedAt        time.Time     `json:"created_at" example:"2023-01-01T12:00:00Z"` // 创建时间
	UserType         enum.UserType `json:"user_type" example:"1"`                     // 用户类型 1:普通用户 2:中级会员 3:高级会员
}

// TokenResponse 令牌响应视图对象
type TokenResponse struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
}

// SimpleUserResponse 简化的用户信息响应视图对象
type SimpleUserResponse struct {
	ID           uint   `json:"id" example:"1"`
	Username     string `json:"username" example:"johndoe"`
	Avatar       string `json:"avatar" example:"http://example.com/avatar.jpg"`
	IsTeamMember bool   `json:"is_team_member" example:"false"` // 是否已加入当前团队
}
