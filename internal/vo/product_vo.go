package vo

// ProductCategoryResponse 产品分类响应视图对象
type ProductCategoryResponse struct {
	ID       uint                     `json:"id" example:"1"`
	Name     string                   `json:"name" example:"电商卡"`
	Children []ProductSummaryResponse `json:"children"`
}

// ProductSummaryResponse 产品摘要响应视图对象
type ProductSummaryResponse struct {
	ID        uint   `json:"id" example:"1"`
	Name      string `json:"name" example:"京东E卡"`
	ImageURL1 string `json:"image_url1" example:"https://example.com/image.jpg"`
}

// ProductSearchResponse 产品搜索响应视图对象
type ProductSearchResponse struct {
	ID          uint   `json:"id" example:"1"`
	Name        string `json:"name" example:"京东E卡"`
	ImageURL1   string `json:"image_url1" example:"https://example.com/image.jpg"`
	DiscountTip string `json:"discount_tip" example:"9.5折起"`
}

// ProductSearchListResponse 产品搜索列表响应视图对象
type ProductSearchListResponse struct {
	List []ProductSearchResponse `json:"list"`
}
