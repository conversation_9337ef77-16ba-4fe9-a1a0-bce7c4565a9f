package api

import (
	"strings"

	"recycle-server/config"
	"recycle-server/internal/dto"
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/pkg"
	"recycle-server/internal/response"
	"recycle-server/internal/services"
	"recycle-server/internal/vo"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// UserController 用户控制器
type UserController struct {
	userService services.UserService
	jwtService  pkg.JWTService
	config      *config.Config
	redisClient *redis.Client
}

// NewUserController 创建用户控制器
func NewUserController(
	userService services.UserService,
	jwtService pkg.JWTService,
	cfg *config.Config,
	redisClient *redis.Client,
) *UserController {
	return &UserController{
		userService: userService,
		jwtService:  jwtService,
		config:      cfg,
		redisClient: redisClient,
	}
}

// GetUserInfo 获取当前登录用户信息
// @Summary 获取当前登录用户信息
// @Description 获取当前登录用户的详细信息
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.UserResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "用户不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/info [get]
func (c *UserController) GetUserInfo(ctx *gin.Context) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	response.SuccessJSON(ctx, "获取成功", convertToUserResponse(user))
}

// Logout 退出登录
// @Summary 退出登录
// @Description 用户退出登录，清除登录状态
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse "退出成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Security ApiKeyAuth
// @Router /api/users/logout [post]
func (c *UserController) Logout(ctx *gin.Context) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	// 获取token
	authHeader := ctx.GetHeader("Authorization")
	token := strings.TrimPrefix(authHeader, "Bearer ")

	// 调用服务层的Logout方法
	err := c.userService.Logout(ctx, user, token)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON[any](ctx, "退出成功", nil)
}

// UpdateUsername 修改用户名
// @Summary 修改用户名
// @Description 修改当前登录用户的用户名
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Param request body dto.UpdateUsernameRequest true "修改用户名请求"
// @Success 200 {object} vo.SuccessAPIResponse "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/username [put]
func (c *UserController) UpdateUsername(ctx *gin.Context, req dto.UpdateUsernameRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层更新用户名
	err := c.userService.UpdateUsername(user.ID, req.Username)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "用户名修改成功")
}

// UpdateAvatar 修改用户头像
// @Summary 修改用户头像
// @Description 修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB
// @Tags API/用户管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "头像文件"
// @Success 200 {object} vo.SuccessAPIResponse "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 413 {object} vo.ErrorAPIResponse "文件过大"
// @Failure 415 {object} vo.ErrorAPIResponse "不支持的文件类型"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/avatar [put]
func (c *UserController) UpdateAvatar(ctx *gin.Context, req dto.UpdateAvatarRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层处理文件上传和头像更新
	err := c.userService.UpdateAvatarWithFile(ctx, user.ID, req.File)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "头像修改成功")
}

// convertToUserResponse 将用户模型转换为响应VO
func convertToUserResponse(user *models.User) vo.UserResponse {
	return vo.UserResponse{
		ID:               user.ID,
		Username:         user.Username,
		Phone:            user.Phone,
		OpenID:           user.OpenID,
		Avatar:           user.Avatar,
		Balance:          user.Balance,
		FriendCount:      user.FriendCount,
		PromotionEarning: user.PromotionEarning,
		CreatedAt:        user.CreatedAt,
		UserType:         user.UserType,
	}
}
