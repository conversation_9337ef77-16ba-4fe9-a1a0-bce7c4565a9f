package api

import (
	"recycle-server/internal/response"
	"recycle-server/internal/services"
	"recycle-server/internal/vo"

	"github.com/gin-gonic/gin"
)

// CommonController 通用控制器
type CommonController struct {
	helpService services.HelpService
}

// NewCommonController 创建通用控制器实例
func NewCommonController(helpService services.HelpService) *CommonController {
	return &CommonController{
		helpService: helpService,
	}
}

// SystemConfig 获取系统配置
// @Summary 获取系统配置
// @Description 获取系统配置信息，包括热门搜索等
// @Tags API/通用接口
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.SystemConfigResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/common/system-config [get]
func (c *CommonController) SystemConfig(ctx *gin.Context) {
	// 创建系统配置响应
	config := vo.SystemConfigResponse{
		HotSearch: []string{
			"京东e卡",
			"携程",
			"微信立减金",
			"支付宝消费券",
			"大润发",
			"美通",
			"天虹",
			"万通金券",
			"天猫",
			"沃尔玛",
			"瑞祥",
			"美团",
		},
	}

	response.SuccessJSON(ctx, "获取系统配置成功", config)
}

// GetAllHelps 获取所有帮助信息
// @Summary 获取所有帮助信息
// @Description 获取所有帮助信息，按排序字段升序排列
// @Tags API/通用接口
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.HelpListResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/common/helps [get]
func (c *CommonController) GetAllHelps(ctx *gin.Context) {
	// 调用服务层获取所有帮助信息
	result, err := c.helpService.GetAllHelps()
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取帮助信息成功", result)
}
