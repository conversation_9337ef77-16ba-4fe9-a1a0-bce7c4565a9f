package api

import (
	"recycle-server/config"
	"recycle-server/internal/dto"
	"recycle-server/internal/response"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
)

// ProductController 产品控制器
type ProductController struct {
	productService services.ProductService
	config         *config.Config
}

// NewProductController 创建产品控制器
func NewProductController(productService services.ProductService, cfg *config.Config) *ProductController {
	return &ProductController{
		productService: productService,
		config:         cfg,
	}
}

// GetProductsWithCategories 获取带分类分组的产品信息
// @Summary 获取带分类分组的产品信息
// @Description 获取所有分类及其下的产品信息，按分类分组返回
// @Tags API/产品管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=[]vo.ProductCategoryResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/products/categories [get]
func (c *ProductController) GetProductsWithCategories(ctx *gin.Context) {
	// 调用服务层获取数据
	result, err := c.productService.GetProductsWithCategories()
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", result)
}

// SearchProducts 搜索产品
// @Summary 搜索产品
// @Description 根据关键词模糊搜索产品名称，返回匹配的产品列表
// @Tags API/产品管理
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.ProductSearchListResponse} "搜索成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/products/search [get]
func (c *ProductController) SearchProducts(ctx *gin.Context, req dto.ProductSearchRequest) {
	// 调用服务层搜索产品
	result, err := c.productService.SearchProducts(req.Keyword)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "搜索成功", result)
}
