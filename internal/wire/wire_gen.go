// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"fmt"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"recycle-server/config"
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/cron"
	"recycle-server/internal/pkg"
	"recycle-server/internal/repository"
	"recycle-server/internal/routes"
	"recycle-server/internal/services"
)

// Injectors from wire.go:

// BuildApplication 构建应用程序
func BuildApplication() (*routes.Router, error) {
	configConfig, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}
	db, err := pkg.NewDatabase(configConfig)
	if err != nil {
		return nil, err
	}
	logger, err := ProvideLogger(configConfig)
	if err != nil {
		return nil, err
	}
	userRepository := repository.NewUserRepository(db, configConfig, logger)
	uploadService := services.NewUploadService(logger)
	client, err := pkg.NewRedisClient(configConfig)
	if err != nil {
		return nil, err
	}
	jwtService := pkg.NewJWTService(configConfig)
	smsService, err := pkg.NewSMSService(configConfig)
	if err != nil {
		return nil, err
	}
	codeService := services.NewCodeService(client, smsService, configConfig, userRepository, jwtService)
	userService := services.NewUserService(userRepository, uploadService, client, jwtService, codeService, configConfig, logger)
	userController := api.NewUserController(userService, jwtService, configConfig, client)
	authController := api.NewAuthController(userService, codeService, jwtService, configConfig)
	uploadController := api.NewUploadController(uploadService, logger)
	productService := services.NewProductService(db, logger, configConfig)
	productController := api.NewProductController(productService, configConfig)
	helpService := services.NewHelpService(db, logger)
	commonController := api.NewCommonController(helpService)
	router := routes.NewRouter(configConfig, userController, authController, uploadController, productController, commonController, jwtService, userService, userRepository, client)
	return router, nil
}

// BuildApplicationWithCron 构建包含定时任务的应用程序
func BuildApplicationWithCron() (*Application, error) {
	configConfig, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}
	db, err := pkg.NewDatabase(configConfig)
	if err != nil {
		return nil, err
	}
	logger, err := ProvideLogger(configConfig)
	if err != nil {
		return nil, err
	}
	userRepository := repository.NewUserRepository(db, configConfig, logger)
	uploadService := services.NewUploadService(logger)
	client, err := pkg.NewRedisClient(configConfig)
	if err != nil {
		return nil, err
	}
	jwtService := pkg.NewJWTService(configConfig)
	smsService, err := pkg.NewSMSService(configConfig)
	if err != nil {
		return nil, err
	}
	codeService := services.NewCodeService(client, smsService, configConfig, userRepository, jwtService)
	userService := services.NewUserService(userRepository, uploadService, client, jwtService, codeService, configConfig, logger)
	userController := api.NewUserController(userService, jwtService, configConfig, client)
	authController := api.NewAuthController(userService, codeService, jwtService, configConfig)
	uploadController := api.NewUploadController(uploadService, logger)
	productService := services.NewProductService(db, logger, configConfig)
	productController := api.NewProductController(productService, configConfig)
	helpService := services.NewHelpService(db, logger)
	commonController := api.NewCommonController(helpService)
	router := routes.NewRouter(configConfig, userController, authController, uploadController, productController, commonController, jwtService, userService, userRepository, client)
	cronService := cron.NewCronService(configConfig, logger, userRepository, client)
	application := &Application{
		Router:      router,
		CronService: cronService,
	}
	return application, nil
}

// BuildScriptService 构建脚本服务
func BuildScriptService() (ScriptService, error) {
	configConfig, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}
	logger, err := ProvideLogger(configConfig)
	if err != nil {
		return nil, err
	}
	db, err := pkg.NewDatabase(configConfig)
	if err != nil {
		return nil, err
	}
	client, err := pkg.NewRedisClient(configConfig)
	if err != nil {
		return nil, err
	}
	userRepository := repository.NewUserRepository(db, configConfig, logger)
	uploadService := services.NewUploadService(logger)
	jwtService := pkg.NewJWTService(configConfig)
	smsService, err := pkg.NewSMSService(configConfig)
	if err != nil {
		return nil, err
	}
	codeService := services.NewCodeService(client, smsService, configConfig, userRepository, jwtService)
	userService := services.NewUserService(userRepository, uploadService, client, jwtService, codeService, configConfig, logger)
	payService, err := pkg.NewPayService(configConfig)
	if err != nil {
		return nil, err
	}
	scriptService := NewScriptService(configConfig, logger, db, client, userService, uploadService, codeService, jwtService, smsService, payService)
	return scriptService, nil
}

// wire.go:

// Application 应用程序结构体
type Application struct {
	Router      *routes.Router
	CronService cron.CronService
}

// ProvideLogger 提供日志记录器
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	if cfg.App.Debug {
		logger, err = zap.NewDevelopment()
	} else {
		logger, err = zap.NewProduction()
	}

	if err != nil {
		return nil, fmt.Errorf("创建日志记录器失败: %w", err)
	}

	return logger, nil
}

var BaseSet = wire.NewSet(config.LoadConfig, ProvideLogger)

// ScriptService 脚本服务接口（在scripts包中定义）
type ScriptService interface {
	GetConfig() *config.Config
	GetLogger() *zap.Logger
	GetDB() *gorm.DB
	GetRedis() *redis.Client
	GetUserService() services.UserService
	GetUploadService() services.UploadService
	GetCodeService() services.CodeService
	GetJWTService() pkg.JWTService
	GetSMSService() pkg.SMSService
	GetPayService() *pkg.PayService
}

// NewScriptService 创建脚本服务的构造函数
func NewScriptService(config2 *config.Config,
	logger *zap.Logger,
	db *gorm.DB, redis2 *redis.Client,
	userService services.UserService,
	uploadService services.UploadService,
	codeService services.CodeService,
	jwtService pkg.JWTService,
	smsService pkg.SMSService,
	payService *pkg.PayService,
) ScriptService {
	return &scriptServiceImpl{
		config:        config2,
		logger:        logger,
		db:            db,
		redis:         redis2,
		userService:   userService,
		uploadService: uploadService,
		codeService:   codeService,
		jwtService:    jwtService,
		smsService:    smsService,
		payService:    payService,
	}
}

// scriptServiceImpl 脚本服务实现
type scriptServiceImpl struct {
	config *config.Config
	logger *zap.Logger
	db     *gorm.DB
	redis  *redis.Client

	userService   services.UserService
	uploadService services.UploadService
	codeService   services.CodeService

	jwtService pkg.JWTService
	smsService pkg.SMSService
	payService *pkg.PayService
}

func (s *scriptServiceImpl) GetConfig() *config.Config { return s.config }

func (s *scriptServiceImpl) GetLogger() *zap.Logger { return s.logger }

func (s *scriptServiceImpl) GetDB() *gorm.DB { return s.db }

func (s *scriptServiceImpl) GetRedis() *redis.Client { return s.redis }

func (s *scriptServiceImpl) GetUserService() services.UserService { return s.userService }

func (s *scriptServiceImpl) GetUploadService() services.UploadService { return s.uploadService }

func (s *scriptServiceImpl) GetCodeService() services.CodeService { return s.codeService }

func (s *scriptServiceImpl) GetJWTService() pkg.JWTService { return s.jwtService }

func (s *scriptServiceImpl) GetSMSService() pkg.SMSService { return s.smsService }

func (s *scriptServiceImpl) GetPayService() *pkg.PayService { return s.payService }
