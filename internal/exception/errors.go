package exception

import "net/http"

// 预定义错误 - 通用错误 (100-999)
var (
	// 参数错误
	ErrInvalidParam = New(http.StatusBadRequest, 100, "参数错误")

	// 认证错误
	ErrUnauthorized = New(http.StatusUnauthorized, 101, "未授权")
	ErrTokenInvalid = New(http.StatusUnauthorized, 102, "无效的令牌")
	ErrTokenExpired = New(http.StatusUnauthorized, 103, "令牌已过期")

	// 权限错误
	ErrForbidden = New(http.StatusForbidden, 200, "权限不足")

	// 资源不存在
	ErrNotFound = New(http.StatusNotFound, 300, "资源不存在")

	// 服务器错误
	ErrInternalServer = New(http.StatusInternalServerError, 500, "服务器内部错误")

	// 验证相关错误
	ErrValidationFailed = New(http.StatusBadRequest, 600, "参数验证失败")

	// 频率限制错误
	ErrRateLimit = New(http.StatusTooManyRequests, 700, "请求过于频繁")
)

// 预定义错误 - 用户相关错误 (1000-1099)
var (
	// 用户基本错误
	ErrUserNotFound = New(http.StatusNotFound, 1000, "用户不存在")

	// 用户登录相关错误
	ErrUserLoginFailed    = New(http.StatusInternalServerError, 1010, "用户登录失败")
	ErrTokenGenFailed     = New(http.StatusInternalServerError, 1011, "生成用户令牌失败")
	ErrUserCreateFailed   = New(http.StatusInternalServerError, 1012, "创建用户失败")
	ErrUserUpdateFailed   = New(http.StatusInternalServerError, 1013, "更新用户信息失败")
	ErrUserStatusDisabled = New(http.StatusForbidden, 1015, "用户已禁用")
	ErrPhoneAlreadyBound  = New(http.StatusBadRequest, 1016, "手机号已被其他用户绑定")
	ErrUserNameGenFailed  = New(http.StatusInternalServerError, 1017, "生成用户名失败")

	// 用户名修改相关错误
	ErrUsernameInvalid      = New(http.StatusBadRequest, 1020, "用户名格式不正确")
	ErrUsernameTooShort     = New(http.StatusBadRequest, 1021, "用户名长度不能少于2个字符")
	ErrUsernameTooLong      = New(http.StatusBadRequest, 1022, "用户名长度不能超过20个字符")
	ErrUsernameUpdateFailed = New(http.StatusInternalServerError, 1023, "用户名修改失败")

	// 头像修改相关错误
	ErrAvatarInvalid      = New(http.StatusBadRequest, 1024, "头像URL格式不正确")
	ErrAvatarUpdateFailed = New(http.StatusInternalServerError, 1025, "头像修改失败")
	ErrAvatarUploadFailed = New(http.StatusInternalServerError, 1026, "头像上传失败")
)

// 预定义错误 - 验证码相关错误 (1100-1199)
var (
	ErrSMSCodeInvalid    = New(http.StatusBadRequest, 1100, "短信验证码无效")
	ErrEmailCodeInvalid  = New(http.StatusBadRequest, 1101, "邮箱验证码无效")
	ErrPhoneInvalid      = New(http.StatusBadRequest, 1102, "手机号格式不正确")
	ErrSMSCodeRateLimit  = New(http.StatusTooManyRequests, 1103, "短信发送过于频繁")
	ErrSMSCodeSendFailed = New(http.StatusInternalServerError, 1104, "短信发送失败")
)

// 预定义错误 - 文件相关错误 (1200-1299)
var (
	ErrFileTooLarge         = New(http.StatusBadRequest, 1200, "文件过大")
	ErrFileTypeNotSupported = New(http.StatusBadRequest, 1201, "文件类型不支持")
	ErrFileOpenFailed       = New(http.StatusInternalServerError, 1202, "文件打开失败")
	ErrFileReadFailed       = New(http.StatusInternalServerError, 1203, "文件读取失败")
	ErrFileUploadFailed     = New(http.StatusInternalServerError, 1204, "文件上传失败")
)

// 预定义错误 - 产品相关错误 (1300-1399)
var (
	ErrCategoryQueryFailed = New(http.StatusInternalServerError, 1300, "获取分类列表失败")
	ErrProductQueryFailed  = New(http.StatusInternalServerError, 1301, "获取产品列表失败")
	ErrCategoryNotFound    = New(http.StatusNotFound, 1302, "分类不存在")
	ErrProductNotFound     = New(http.StatusNotFound, 1303, "产品不存在")
)

// 预定义错误 - 帮助相关错误 (1400-1499)
var (
	ErrHelpQueryFailed = New(http.StatusInternalServerError, 1400, "获取帮助信息失败")
	ErrHelpNotFound    = New(http.StatusNotFound, 1401, "帮助信息不存在")
)
